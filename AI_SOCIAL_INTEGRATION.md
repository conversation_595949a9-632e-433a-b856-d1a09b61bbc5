# Postiz AI和社交媒体集成配置指南

## 概述
本指南将帮你配置：
1. **Groq AI集成** - 用于AI内容生成和优化
2. **X平台(Twitter)集成** - 用于社交媒体发布
3. **其他社交媒体平台配置**

## 1. Groq AI集成配置

### 1.1 Groq API配置
我们已经在`.env`文件中配置了Groq API：

```env
# Groq AI配置
AI_PROVIDER="groq"
GROQ_API_KEY="********************************************************"

# 如果Postiz还不支持Groq，使用OpenAI兼容格式
OPENAI_API_KEY="********************************************************"
OPENAI_BASE_URL="https://api.groq.com/openai/v1"
```

### 1.2 验证Groq API连接
创建测试脚本验证Groq连接：

```javascript
// test-groq.js
const fetch = require('node-fetch');

async function testGroq() {
  const apiKey = '********************************************************';
  
  try {
    const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'llama3-8b-8192',
        messages: [
          {
            role: 'user',
            content: '为一个科技公司写一条社交媒体帖子，主题是AI创新。'
          }
        ],
        max_tokens: 150,
        temperature: 0.7
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Groq API连接成功');
      console.log('🤖 AI生成内容:', data.choices[0].message.content);
    } else {
      console.log('❌ Groq API连接失败:', response.status);
      console.log('错误信息:', await response.text());
    }
  } catch (error) {
    console.error('❌ Groq测试失败:', error.message);
  }
}

testGroq();
```

运行测试：
```bash
npm install node-fetch  # 如果需要
node test-groq.js
```

### 1.3 Groq模型配置
Groq支持的主要模型：

```env
# 在.env中添加模型配置
GROQ_MODEL="llama3-8b-8192"  # 默认模型
# 其他可用模型:
# - llama3-70b-8192 (更强大但较慢)
# - mixtral-8x7b-32768 (长上下文)
# - gemma-7b-it (Google Gemma)
```

## 2. X平台(Twitter)集成配置

### 2.1 X平台API配置
我们已经配置了X平台API密钥：

```env
# X平台 API配置
X_API_KEY="*************************"
X_API_SECRET="4RRgnNk3Fst1wKGb6wyfrK32tBjc1nPGmVeejlNsOoIbDZFCO5"
```

### 2.2 验证X平台API连接
创建测试脚本：

```javascript
// test-twitter.js
const crypto = require('crypto');
const fetch = require('node-fetch');

function generateOAuthSignature(method, url, params, consumerSecret, tokenSecret = '') {
  // OAuth 1.0a签名生成逻辑
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}=${encodeURIComponent(params[key])}`)
    .join('&');
  
  const signatureBaseString = `${method}&${encodeURIComponent(url)}&${encodeURIComponent(sortedParams)}`;
  const signingKey = `${encodeURIComponent(consumerSecret)}&${encodeURIComponent(tokenSecret)}`;
  
  return crypto
    .createHmac('sha1', signingKey)
    .update(signatureBaseString)
    .digest('base64');
}

async function testTwitterAPI() {
  const consumerKey = '*************************';
  const consumerSecret = '4RRgnNk3Fst1wKGb6wyfrK32tBjc1nPGmVeejlNsOoIbDZFCO5';
  
  try {
    // 测试API密钥有效性
    console.log('🐦 测试X平台API连接...');
    console.log('✅ API密钥已配置');
    console.log('📝 Consumer Key:', consumerKey);
    console.log('🔐 Consumer Secret:', consumerSecret.substring(0, 10) + '...');
    
    // 注意：完整的Twitter API测试需要用户授权token
    // 这里只验证配置是否正确
    console.log('⚠️  完整测试需要用户授权后进行');
    
  } catch (error) {
    console.error('❌ X平台API测试失败:', error.message);
  }
}

testTwitterAPI();
```

### 2.3 X平台OAuth流程配置
在Postiz中配置X平台OAuth：

1. 确保在Twitter Developer Portal中配置了正确的回调URL
2. 回调URL应该是：`http://localhost:3000/api/auth/twitter/callback`（开发环境）

## 3. 其他社交媒体平台配置

### 3.1 LinkedIn配置（可选）
```env
# LinkedIn API配置
LINKEDIN_CLIENT_ID="your-linkedin-client-id"
LINKEDIN_CLIENT_SECRET="your-linkedin-client-secret"
```

### 3.2 Reddit配置（可选）
```env
# Reddit API配置
REDDIT_CLIENT_ID="your-reddit-client-id"
REDDIT_CLIENT_SECRET="your-reddit-client-secret"
```

### 3.3 GitHub配置（可选）
```env
# GitHub API配置
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"
```

## 4. 在Postiz中测试集成

### 4.1 启动应用并测试AI功能
```bash
# 启动开发服务器
pnpm run dev

# 在另一个终端查看日志
tail -f logs/postiz.log  # 如果有日志文件
```

### 4.2 测试AI功能
1. 访问 http://localhost:4200
2. 登录或注册账户
3. 创建新帖子
4. 查找AI辅助功能（如内容生成、优化建议等）
5. 测试AI生成内容

### 4.3 测试X平台连接
1. 在Postiz中进入"连接账户"或"社交媒体"设置
2. 选择连接X平台(Twitter)
3. 完成OAuth授权流程
4. 测试发布帖子到X平台

## 5. 故障排除

### 5.1 AI功能不工作
检查以下项目：

```bash
# 检查环境变量
echo $GROQ_API_KEY
echo $OPENAI_API_KEY

# 检查网络连接
curl -H "Authorization: Bearer ********************************************************" \
     https://api.groq.com/openai/v1/models
```

### 5.2 X平台连接失败
常见问题：
- API密钥错误
- 回调URL配置错误
- Twitter Developer账户权限不足

### 5.3 调试日志
启用详细日志：

```env
# 在.env中添加
DEBUG="postiz:*"
LOG_LEVEL="debug"
```

## 6. 高级配置

### 6.1 AI提示词自定义
如果Postiz支持自定义AI提示词：

```env
# AI提示词配置
AI_SYSTEM_PROMPT="你是一个专业的社交媒体内容创作助手，专门帮助用户创建吸引人的社交媒体帖子。"
AI_CONTENT_STYLE="专业、友好、有趣"
```

### 6.2 社交媒体发布配置
```env
# 发布配置
AUTO_PUBLISH="false"  # 是否自动发布
DRAFT_MODE="true"     # 默认保存为草稿
MAX_POST_LENGTH="280" # X平台字符限制
```

### 6.3 内容审核配置
```env
# 内容审核
ENABLE_CONTENT_MODERATION="true"
CONTENT_FILTER_LEVEL="moderate"
```

## 7. 验证完整集成

完成配置后，验证以下功能：

### AI功能验证
- [ ] AI内容生成正常工作
- [ ] AI内容优化建议可用
- [ ] AI响应速度合理
- [ ] AI生成内容质量良好

### X平台集成验证
- [ ] 可以成功连接X平台账户
- [ ] 可以发布文本帖子
- [ ] 可以发布带图片的帖子
- [ ] 可以查看发布历史

### 整体功能验证
- [ ] 创建帖子流程完整
- [ ] 多平台同时发布功能
- [ ] 定时发布功能
- [ ] 帖子编辑和管理功能

完成这些验证后，你的Postiz AI和社交媒体集成就完全配置好了！
