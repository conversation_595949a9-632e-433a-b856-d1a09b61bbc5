export default [
  { link: '', name: '' },
  { link: '1c-enterprise', name: '1C Enterprise' },
  { link: 'abap', name: 'ABAP' },
  { link: 'actionscript', name: 'ActionScript' },
  { link: 'adblock-filter-list', name: 'Adblock Filter List' },
  { link: 'al', name: 'AL' },
  { link: 'angelscript', name: 'AngelScript' },
  { link: 'apacheconf', name: 'ApacheConf' },
  { link: 'apex', name: 'Apex' },
  { link: 'apl', name: 'APL' },
  { link: 'applescript', name: 'AppleScript' },
  { link: 'arc', name: 'Arc' },
  { link: 'asl', name: 'ASL' },
  { link: 'classic-asp', name: 'Classic ASP' },
  { link: 'assembly', name: 'Assembly' },
  { link: 'astro', name: 'As<PERSON>' },
  { link: 'autohotkey', name: 'AutoHotkey' },
  { link: 'autoit', name: 'AutoIt' },
  { link: 'awk', name: 'Awk' },
  { link: 'batchfile', name: '<PERSON>ch<PERSON><PERSON>' },
  { link: 'bicep', name: '<PERSON><PERSON><PERSON>' },
  { link: 'bikeshed', name: '<PERSON><PERSON><PERSON>' },
  { link: 'bitbake', name: 'BitBake' },
  { link: 'blade', name: 'Blade' },
  { link: 'boo', name: 'Boo' },
  { link: 'brainfuck', name: 'Brainfuck' },
  { link: 'brighterscript', name: 'BrighterScript' },
  { link: 'c', name: 'C' },
  { link: 'c%23', name: 'C#' },
  { link: 'c++', name: 'C++' },
  { link: 'cairo', name: 'Cairo' },
  { link: "cap'n-proto", name: "Cap'n Proto" },
  { link: 'cartocss', name: 'CartoCSS' },
  { link: 'chapel', name: 'Chapel' },
  { link: 'circom', name: 'Circom' },
  { link: 'classic-asp', name: 'Classic ASP' },
  { link: 'clojure', name: 'Clojure' },
  { link: 'cmake', name: 'CMake' },
  { link: 'codeql', name: 'CodeQL' },
  { link: 'coffeescript', name: 'CoffeeScript' },
  { link: 'common-lisp', name: 'Common Lisp' },
  { link: 'component-pascal', name: 'Component Pascal' },
  { link: 'crystal', name: 'Crystal' },
  { link: 'css', name: 'CSS' },
  { link: 'cuda', name: 'Cuda' },
  { link: 'cue', name: 'CUE' },
  { link: 'cython', name: 'Cython' },
  { link: 'd', name: 'D' },
  { link: 'dart', name: 'Dart' },
  { link: 'denizenscript', name: 'DenizenScript' },
  { link: 'digital-command-language', name: 'DIGITAL Command Language' },
  { link: 'dm', name: 'DM' },
  { link: 'dockerfile', name: 'Dockerfile' },
  { link: 'earthly', name: 'Earthly' },
  { link: 'ejs', name: 'EJS' },
  { link: 'elixir', name: 'Elixir' },
  { link: 'elm', name: 'Elm' },
  { link: 'emacs-lisp', name: 'Emacs Lisp' },
  { link: 'emberscript', name: 'EmberScript' },
  { link: 'erlang', name: 'Erlang' },
  { link: 'f%23', name: 'F#' },
  { link: 'f*', name: 'F*' },
  { link: 'fennel', name: 'Fennel' },
  { link: 'fluent', name: 'Fluent' },
  { link: 'forth', name: 'Forth' },
  { link: 'fortran', name: 'Fortran' },
  { link: 'freemarker', name: 'FreeMarker' },
  { link: 'g-code', name: 'G-code' },
  { link: 'gdscript', name: 'GDScript' },
  { link: 'gherkin', name: 'Gherkin' },
  { link: 'gleam', name: 'Gleam' },
  { link: 'glsl', name: 'GLSL' },
  { link: 'go', name: 'Go' },
  { link: 'groovy', name: 'Groovy' },
  { link: 'hack', name: 'Hack' },
  { link: 'handlebars', name: 'Handlebars' },
  { link: 'haskell', name: 'Haskell' },
  { link: 'haxe', name: 'Haxe' },
  { link: 'hcl', name: 'HCL' },
  { link: 'hlsl', name: 'HLSL' },
  { link: 'holyc', name: 'HolyC' },
  { link: 'hoon', name: 'hoon' },
  { link: 'hosts-file', name: 'Hosts File' },
  { link: 'html', name: 'HTML' },
  { link: 'idris', name: 'Idris' },
  { link: 'inform-7', name: 'Inform 7' },
  { link: 'inno-setup', name: 'Inno Setup' },
  { link: 'io', name: 'Io' },
  { link: 'java', name: 'Java' },
  { link: 'javascript', name: 'JavaScript' },
  { link: 'json', name: 'JSON' },
  { link: 'jsonnet', name: 'Jsonnet' },
  { link: 'julia', name: 'Julia' },
  { link: 'jupyter-notebook', name: 'Jupyter Notebook' },
  { link: 'just', name: 'Just' },
  { link: 'kicad-layout', name: 'KiCad Layout' },
  { link: 'kotlin', name: 'Kotlin' },
  { link: 'labview', name: 'LabVIEW' },
  { link: 'lean', name: 'Lean' },
  { link: 'less', name: 'Less' },
  { link: 'lfe', name: 'LFE' },
  { link: 'liquid', name: 'Liquid' },
  { link: 'llvm', name: 'LLVM' },
  { link: 'logos', name: 'Logos' },
  { link: 'lookml', name: 'LookML' },
  { link: 'lua', name: 'Lua' },
  { link: 'm4', name: 'M4' },
  { link: 'makefile', name: 'Makefile' },
  { link: 'markdown', name: 'Markdown' },
  { link: 'mathematica', name: 'Mathematica' },
  { link: 'matlab', name: 'MATLAB' },
  { link: 'mcfunction', name: 'mcfunction' },
  { link: 'mdx', name: 'MDX' },
  { link: 'mermaid', name: 'Mermaid' },
  { link: 'meson', name: 'Meson' },
  { link: 'metal', name: 'Metal' },
  { link: 'mlir', name: 'MLIR' },
  { link: 'move', name: 'Move' },
  { link: 'mustache', name: 'Mustache' },
  { link: 'nasl', name: 'NASL' },
  { link: 'nesc', name: 'nesC' },
  { link: 'nextflow', name: 'Nextflow' },
  { link: 'nim', name: 'Nim' },
  { link: 'nix', name: 'Nix' },
  { link: 'nsis', name: 'NSIS' },
  { link: 'nunjucks', name: 'Nunjucks' },
  { link: 'objective-c', name: 'Objective-C' },
  { link: 'objective-c++', name: 'Objective-C++' },
  { link: 'ocaml', name: 'OCaml' },
  { link: 'odin', name: 'Odin' },
  { link: 'open-policy-agent', name: 'Open Policy Agent' },
  { link: 'openscad', name: 'OpenSCAD' },
  { link: 'papyrus', name: 'Papyrus' },
  { link: 'pascal', name: 'Pascal' },
  { link: 'perl', name: 'Perl' },
  { link: 'php', name: 'PHP' },
  { link: 'plpgsql', name: 'PLpgSQL' },
  { link: 'plsql', name: 'PLSQL' },
  { link: 'pony', name: 'Pony' },
  { link: 'postscript', name: 'PostScript' },
  { link: 'powershell', name: 'PowerShell' },
  { link: 'processing', name: 'Processing' },
  { link: 'prolog', name: 'Prolog' },
  { link: 'pug', name: 'Pug' },
  { link: 'puppet', name: 'Puppet' },
  { link: 'purebasic', name: 'PureBasic' },
  { link: 'purescript', name: 'PureScript' },
  { link: 'python', name: 'Python' },
  { link: 'qml', name: 'QML' },
  { link: 'r', name: 'R' },
  { link: 'racket', name: 'Racket' },
  { link: 'raku', name: 'Raku' },
  { link: 'raml', name: 'RAML' },
  { link: "ren'py", name: "Ren'Py" },
  { link: 'rescript', name: 'ReScript' },
  { link: 'restructuredtext', name: 'reStructuredText' },
  { link: 'rich-text-format', name: 'Rich Text Format' },
  { link: 'robotframework', name: 'RobotFramework' },
  { link: 'roff', name: 'Roff' },
  { link: 'routeros-script', name: 'RouterOS Script' },
  { link: 'rpm-spec', name: 'RPM Spec' },
  { link: 'ruby', name: 'Ruby' },
  { link: 'rust', name: 'Rust' },
  { link: 'sass', name: 'Sass' },
  { link: 'scala', name: 'Scala' },
  { link: 'scheme', name: 'Scheme' },
  { link: 'scss', name: 'SCSS' },
  { link: 'shaderlab', name: 'ShaderLab' },
  { link: 'shell', name: 'Shell' },
  { link: 'smali', name: 'Smali' },
  { link: 'smalltalk', name: 'Smalltalk' },
  { link: 'smarty', name: 'Smarty' },
  { link: 'solidity', name: 'Solidity' },
  { link: 'sqf', name: 'SQF' },
  { link: 'sql', name: 'SQL' },
  { link: 'squirrel', name: 'Squirrel' },
  { link: 'standard-ml', name: 'Standard ML' },
  { link: 'starlark', name: 'Starlark' },
  { link: 'stylus', name: 'Stylus' },
  { link: 'supercollider', name: 'SuperCollider' },
  { link: 'svelte', name: 'Svelte' },
  { link: 'svg', name: 'SVG' },
  { link: 'swift', name: 'Swift' },
  { link: 'swig', name: 'SWIG' },
  { link: 'systemverilog', name: 'SystemVerilog' },
  { link: 'tcl', name: 'Tcl' },
  { link: 'tex', name: 'TeX' },
  { link: 'text', name: 'Text' },
  { link: 'thrift', name: 'Thrift' },
  { link: 'tsql', name: 'TSQL' },
  { link: 'twig', name: 'Twig' },
  { link: 'typescript', name: 'TypeScript' },
  { link: 'typst', name: 'Typst' },
  { link: 'unrealscript', name: 'UnrealScript' },
  { link: 'v', name: 'V' },
  { link: 'vala', name: 'Vala' },
  { link: 'vbscript', name: 'VBScript' },
  { link: 'verilog', name: 'Verilog' },
  { link: 'vhdl', name: 'VHDL' },
  { link: 'vim-script', name: 'Vim Script' },
  { link: 'vim-snippet', name: 'Vim Snippet' },
  { link: 'visual-basic-.net', name: 'Visual Basic .NET' },
  { link: 'visual-basic-.net', name: 'Visual Basic .NET' },
  { link: 'vue', name: 'Vue' },
  { link: 'webassembly', name: 'WebAssembly' },
  { link: 'wgsl', name: 'WGSL' },
  { link: 'witcher-script', name: 'Witcher Script' },
  { link: 'xc', name: 'XC' },
  { link: 'xslt', name: 'XSLT' },
  { link: 'yacc', name: 'Yacc' },
  { link: 'yaml', name: 'YAML' },
  { link: 'yara', name: 'YARA' },
  { link: 'zap', name: 'ZAP' },
  { link: 'zenscript', name: 'ZenScript' },
  { link: 'zig', name: 'Zig' },
];
