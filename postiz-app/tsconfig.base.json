{"compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "strictPropertyInitialization": false, "experimentalDecorators": true, "noPropertyAccessFromIndexSignature": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "incremental": true, "strictNullChecks": false, "noImplicitAny": true, "strictBindCallApply": false, "noFallthroughCasesInSwitch": true, "strict": true, "paths": {"@gitroom/backend/*": ["apps/backend/src/*"], "@gitroom/cron/*": ["apps/cron/src/*"], "@gitroom/frontend/*": ["apps/frontend/src/*"], "@gitroom/helpers/*": ["libraries/helpers/src/*"], "@gitroom/nestjs-libraries/*": ["libraries/nestjs-libraries/src/*"], "@gitroom/react/*": ["libraries/react-shared-libraries/src/*"], "@gitroom/plugins/*": ["libraries/plugins/src/*"], "@gitroom/workers/*": ["apps/workers/src/*"], "@gitroom/extension/*": ["apps/extension/src/*"]}}, "exclude": ["node_modules", "tmp"]}