module.exports = {
  apps: [
    {
      name: 'postiz-backend',
      script: 'pnpm',
      args: 'run dev:backend',
      cwd: '/www/wwwroot/su.guiyunai.fun/postiz-app',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      error_file: '/var/log/pm2/postiz-backend-error.log',
      out_file: '/var/log/pm2/postiz-backend-out.log',
      log_file: '/var/log/pm2/postiz-backend.log',
      time: true
    },
    {
      name: 'postiz-frontend',
      script: 'pnpm',
      args: 'run dev:frontend',
      cwd: '/www/wwwroot/su.guiyunai.fun/postiz-app',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'development',
        PORT: 4200
      },
      error_file: '/var/log/pm2/postiz-frontend-error.log',
      out_file: '/var/log/pm2/postiz-frontend-out.log',
      log_file: '/var/log/pm2/postiz-frontend.log',
      time: true
    },
    {
      name: 'postiz-workers',
      script: 'pnpm',
      args: 'run dev:workers',
      cwd: '/www/wwwroot/su.guiyunai.fun/postiz-app',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      env: {
        NODE_ENV: 'development'
      },
      error_file: '/var/log/pm2/postiz-workers-error.log',
      out_file: '/var/log/pm2/postiz-workers-out.log',
      log_file: '/var/log/pm2/postiz-workers.log',
      time: true
    }
  ]
};
