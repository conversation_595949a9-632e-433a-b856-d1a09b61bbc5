---
name: "Code Quality  Analysis"

on:
  push:
    branches:
      - dev1
    paths:
      - apps/**
      - '!apps/docs/**'
      - libraries/**

jobs:
  analyze:
    name: Analyze (${{ matrix.language }})

    runs-on: 'ubuntu-latest'
    permissions:
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        include:
          - language: javascript-typescript
            build-mode: none

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}
          build-mode: ${{ matrix.build-mode }}

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:${{matrix.language}}"
