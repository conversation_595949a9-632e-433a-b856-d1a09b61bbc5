'use client';

export const Logo = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="60"
      height="60"
      viewBox="0 0 60 60"
      fill="none"
      className="mt-[8px]"
    >
      <path
        d="M12.8816 11.4648C12.9195 12.0781 12.9731 12.7698 13.0342 13.5594L15.2408 42.0719C15.4874 45.2588 15.6107 46.8522 16.3251 48.0214C16.9535 49.0498 17.8913 49.853 19.0042 50.3156C20.2694 50.8416 21.8629 50.7183 25.0497 50.4717L46.8877 48.7817C48.9537 48.6218 50.3501 48.5137 51.3952 48.2628C51.0447 49.0858 50.5039 49.8189 49.8121 50.3992C48.7623 51.2797 47.205 51.6389 44.0905 52.3574L22.7476 57.2805C19.633 57.9989 18.0757 58.3581 16.7463 58.0264C15.5769 57.7346 14.5299 57.0801 13.7554 56.1566C12.8749 55.1069 12.5156 53.5496 11.7972 50.435L5.36942 22.569C4.651 19.4544 4.29178 17.8972 4.62351 16.5677C4.91531 15.3983 5.56982 14.3513 6.49325 13.5768C7.54303 12.6963 9.10032 12.3371 12.2149 11.6186L12.8816 11.4648Z"
        fill="#612BD3"
      />
      <path
        d="M47.0122 2.5752C47.9217 2.57909 48.5299 2.67533 49.0386 2.88672C50.0099 3.29052 50.829 3.99206 51.3774 4.88965C51.6647 5.35982 51.8537 5.94554 51.9976 6.84375C52.1427 7.7505 52.2327 8.91127 52.3569 10.5166L54.564 39.0283C54.6882 40.6337 54.7769 41.7946 54.7729 42.7129C54.7691 43.6226 54.6729 44.2305 54.4614 44.7393C54.0576 45.7105 53.3561 46.5287 52.4585 47.0771C51.9883 47.3644 51.4027 47.5534 50.5044 47.6973C49.5977 47.8424 48.4376 47.9334 46.8325 48.0576L24.9937 49.748C23.3886 49.8723 22.2282 49.961 21.3101 49.957C20.4003 49.9531 19.7925 49.8561 19.2837 49.6445C18.3124 49.2407 17.4933 48.5402 16.9448 47.6426C16.6576 47.1724 16.4685 46.5867 16.3247 45.6885C16.1795 44.7817 16.0896 43.621 15.9653 42.0156L13.7583 13.5029C13.6341 11.8979 13.5454 10.7375 13.5493 9.81934C13.5532 8.90971 13.6494 8.30172 13.8608 7.79297C14.2646 6.82169 14.9662 6.00253 15.8638 5.4541C16.3339 5.16692 16.9197 4.97778 17.8179 4.83398C18.7246 4.68882 19.8854 4.59884 21.4907 4.47461L43.3286 2.78418C44.9336 2.65997 46.094 2.57129 47.0122 2.5752Z"
        stroke="#131019"
        stroke-width="1.45254"
      />
      <path
        d="M21.5681 5.49237L43.4061 3.80233C45.0283 3.67679 46.1402 3.59211 47.007 3.59582C47.8534 3.59945 48.3108 3.69053 48.6454 3.82963C49.4173 4.15056 50.0679 4.70763 50.5037 5.421C50.6927 5.7302 50.853 6.16816 50.9868 7.00389C51.1239 7.85984 51.2113 8.97152 51.3368 10.5937L53.5434 39.1062C53.6689 40.7284 53.7536 41.8403 53.7499 42.7071C53.7463 43.5535 53.6552 44.0109 53.5161 44.3455C53.1952 45.1174 52.6381 45.7679 51.9247 46.2038C51.6155 46.3927 51.1776 46.5531 50.3418 46.6869C49.4859 46.824 48.3742 46.9114 46.752 47.0369L24.914 48.7269C23.2918 48.8525 22.1799 48.9372 21.3131 48.9335C20.4667 48.9298 20.0093 48.8387 19.6747 48.6996C18.9028 48.3787 18.2522 47.8216 17.8164 47.1083C17.6274 46.7991 17.4671 46.3611 17.3333 45.5254C17.1962 44.6694 17.1088 43.5578 16.9833 41.9356L14.7767 13.4231C14.6512 11.8009 14.5665 10.689 14.5702 9.82217C14.5738 8.97581 14.6649 8.51838 14.804 8.1838C15.1249 7.41186 15.682 6.76133 16.3954 6.32545C16.7046 6.13653 17.1425 5.97616 17.9783 5.84235C18.8342 5.70531 19.9459 5.61791 21.5681 5.49237Z"
        fill="white"
      />
      <path
        d="M31.0188 12.0956L31.2277 14.7969C31.6025 14.3332 32.0909 13.9331 32.6929 13.5967C33.2931 13.2362 34.0374 13.0217 34.9259 12.953C35.7423 12.8899 36.5227 12.9865 37.2672 13.243C38.0357 13.4976 38.723 13.9517 39.3291 14.6054C39.9574 15.2332 40.4832 16.0983 40.9067 17.2009C41.3301 18.3035 41.604 19.6592 41.7284 21.268C41.8175 22.4205 41.7985 23.5814 41.6715 24.7507C41.5685 25.9182 41.3002 26.9776 40.8665 27.929C40.4328 28.8805 39.806 29.6777 38.9861 30.3209C38.1884 30.9381 37.1532 31.2959 35.8806 31.3943C34.9681 31.4649 34.2385 31.4005 33.6917 31.2012C33.143 30.9779 32.7236 30.7084 32.4335 30.3927L33.1102 39.145L28.0238 40.8427L25.8323 12.4966L31.0188 12.0956ZM33.9095 28.3944C34.5338 28.3462 35.0463 28.1012 35.4469 27.6596C35.8457 27.194 36.1392 26.6157 36.3273 25.9248C36.5395 25.2321 36.662 24.4738 36.6949 23.6499C36.75 22.8002 36.746 21.9672 36.6829 21.1508C36.5808 19.8301 36.38 18.7949 36.0804 18.0451C35.8049 17.2934 35.4972 16.7495 35.1572 16.4135C34.8153 16.0534 34.4846 15.8374 34.165 15.7655C33.8694 15.6918 33.6376 15.6614 33.4695 15.6744C33.0373 15.7078 32.6292 15.8963 32.2451 16.2401C31.8592 16.5598 31.5934 17.0272 31.4477 17.6423L32.2246 27.6913C32.3595 27.8741 32.5665 28.0514 32.8455 28.2231C33.1226 28.3707 33.4773 28.4278 33.9095 28.3944Z"
        fill="#131019"
      />
    </svg>
  );
};
