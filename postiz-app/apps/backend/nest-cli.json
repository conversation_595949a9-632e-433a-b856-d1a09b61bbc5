{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "monorepo": false, "sourceRoot": "src", "entryFile": "../../dist/backend/apps/backend/src/main", "language": "ts", "generateOptions": {"spec": false}, "compilerOptions": {"manualRestart": true, "tsConfigPath": "./tsconfig.build.json", "webpack": false, "deleteOutDir": true, "assets": [], "watchAssets": false, "plugins": []}}