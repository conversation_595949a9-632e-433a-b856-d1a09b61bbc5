#!/bin/bash

# Postiz 启动脚本
# 用于启动所有必要的服务

echo "🚀 启动 Postiz 服务..."

# 设置工作目录
cd /www/wwwroot/su.guiyunai.fun/postiz-app

# 检查环境
echo "📋 检查环境..."
node --version
pnpm --version

# 检查数据库连接
echo "🗄️ 检查数据库连接..."
PGPASSWORD=postiz-password psql -h localhost -U postiz-user -d postiz-db-local -c "SELECT 1;" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ PostgreSQL 连接正常"
else
    echo "❌ PostgreSQL 连接失败"
    exit 1
fi

redis-cli ping > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Redis 连接正常"
else
    echo "❌ Redis 连接失败"
    exit 1
fi

# 停止现有的PM2进程
echo "🛑 停止现有服务..."
pm2 delete all > /dev/null 2>&1 || true

# 启动前端服务（限制内存）
echo "🎨 启动前端服务..."
NODE_OPTIONS="--max-old-space-size=1024" pm2 start pnpm --name "postiz-frontend" -- run dev:frontend

# 等待前端启动
sleep 15

# 启动后端服务（限制内存）
echo "⚙️ 启动后端服务..."
NODE_OPTIONS="--max-old-space-size=1024" pm2 start pnpm --name "postiz-backend" -- run dev:backend

# 等待后端启动
sleep 10

# 启动工作进程（限制内存）
echo "👷 启动工作进程..."
NODE_OPTIONS="--max-old-space-size=512" pm2 start pnpm --name "postiz-workers" -- run dev:workers

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 20

# 检查服务状态
echo "📊 检查服务状态..."
pm2 status

# 测试服务连接
echo "🔍 测试服务连接..."

# 测试前端
curl -I http://localhost:4200 > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 前端服务 (4200) 正常"
else
    echo "⚠️ 前端服务 (4200) 可能还在启动中"
fi

# 测试后端
curl -I http://localhost:3000/health > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 后端服务 (3000) 正常"
else
    echo "⚠️ 后端服务 (3000) 可能还在启动中"
fi

# 测试HTTPS访问
curl -I https://su.guiyunai.fun > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ HTTPS 访问正常"
else
    echo "❌ HTTPS 访问失败"
fi

echo ""
echo "🎉 Postiz 部署完成！"
echo ""
echo "📱 访问地址："
echo "   - 主站: https://su.guiyunai.fun"
echo "   - 前端: http://localhost:4200"
echo "   - 后端: http://localhost:3000"
echo ""
echo "📋 管理命令："
echo "   - 查看状态: pm2 status"
echo "   - 查看日志: pm2 logs"
echo "   - 重启服务: pm2 restart all"
echo "   - 停止服务: pm2 stop all"
echo ""
echo "🔧 故障排除："
echo "   - 如果后端无法启动，请检查: pm2 logs postiz-backend"
echo "   - 如果前端无法访问，请检查: pm2 logs postiz-frontend"
echo "   - 数据库问题请检查: systemctl status postgresql"
echo ""
