# Postiz 原生开发环境设置指南

## 前置要求

### 系统要求
- **操作系统**: Linux/macOS (推荐) 或 Windows (WSL2)
- **Node.js**: 版本 18+ 
- **pnpm**: 包管理器
- **Git**: 版本控制
- **Docker**: 用于运行数据库服务

### 检查环境
```bash
# 检查Node.js版本
node --version  # 应该 >= 18.0.0

# 检查pnpm
pnpm --version

# 检查Docker
docker --version
```

## 第一步：安装必要软件

### 1. 安装Node.js (如果没有)
```bash
# 使用nvm安装Node.js 18+
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 18
nvm use 18
```

### 2. 安装pnpm
```bash
npm install -g pnpm
```

### 3. 安装Docker (如果没有)
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 添加用户到docker组
sudo usermod -aG docker $USER
# 重新登录或运行: newgrp docker
```

## 第二步：启动数据库服务

### 启动PostgreSQL和Redis
```bash
# 启动PostgreSQL
docker run -d \
  --name postiz-postgres \
  -e POSTGRES_USER=postiz-user \
  -e POSTGRES_PASSWORD=postiz-password \
  -e POSTGRES_DB=postiz-db-local \
  -p 5432:5432 \
  postgres:15-alpine

# 启动Redis
docker run -d \
  --name postiz-redis \
  -p 6379:6379 \
  redis:7-alpine

# 验证服务运行
docker ps
```

### 验证数据库连接
```bash
# 测试PostgreSQL连接
docker exec -it postiz-postgres psql -U postiz-user -d postiz-db-local -c "SELECT version();"

# 测试Redis连接
docker exec -it postiz-redis redis-cli ping
```

## 第三步：克隆和配置Postiz

### 1. 克隆仓库
```bash
# 克隆官方仓库
git clone https://github.com/gitroomhq/postiz-app.git
cd postiz-app

# 查看最新稳定版本
git tag --sort=-version:refname | head -5
# 切换到最新稳定版本（可选）
# git checkout v1.65.7
```

### 2. 复制配置文件
```bash
# 复制我们创建的.env文件到项目根目录
cp /path/to/your/.env .env

# 或者手动创建.env文件，内容使用我们之前生成的配置
```

### 3. 安装依赖
```bash
# 安装项目依赖
pnpm install

# 如果遇到权限问题
sudo chown -R $USER:$USER node_modules
```

## 第四步：初始化数据库

### 1. 生成Prisma客户端并运行迁移
```bash
# 生成Prisma客户端
pnpm run prisma-generate

# 推送数据库架构
pnpm run prisma-db-push

# 查看数据库状态
pnpm run prisma-studio
# 这会在 http://localhost:5555 打开数据库管理界面
```

### 2. 验证数据库初始化
```bash
# 检查数据库表
docker exec -it postiz-postgres psql -U postiz-user -d postiz-db-local -c "\dt"
```

## 第五步：启动开发服务器

### 1. 启动应用
```bash
# 启动开发服务器
pnpm run dev

# 或者分别启动前端和后端
# pnpm run dev:frontend  # 端口 4200
# pnpm run dev:backend   # 端口 3000
```

### 2. 验证服务启动
```bash
# 检查服务状态
curl http://localhost:3000/health
curl http://localhost:4200
```

## 第六步：访问应用

### 打开浏览器访问
- **前端界面**: http://localhost:4200
- **后端API**: http://localhost:3000
- **数据库管理**: http://localhost:5555 (Prisma Studio)

### 创建第一个用户
1. 访问 http://localhost:4200
2. 点击注册按钮
3. 填写用户信息完成注册

## 常见问题解决

### 1. 端口冲突
```bash
# 查看端口占用
lsof -i :4200
lsof -i :3000
lsof -i :5432
lsof -i :6379

# 杀死占用进程
kill -9 <PID>
```

### 2. 数据库连接失败
```bash
# 重启数据库容器
docker restart postiz-postgres postiz-redis

# 检查数据库日志
docker logs postiz-postgres
docker logs postiz-redis
```

### 3. 依赖安装失败
```bash
# 清理缓存重新安装
rm -rf node_modules
rm pnpm-lock.yaml
pnpm install
```

### 4. Prisma相关问题
```bash
# 重新生成Prisma客户端
pnpm run prisma-generate

# 重置数据库
pnpm run prisma-db-push --force-reset
```

## 开发工作流

### 日常开发
```bash
# 启动所有服务
docker start postiz-postgres postiz-redis
pnpm run dev

# 查看日志
pnpm run dev --verbose

# 运行测试
pnpm run test
```

### 代码格式化
```bash
# 格式化代码
pnpm run format

# 检查代码规范
pnpm run lint
```

## 下一步

1. ✅ 验证图片上传功能
2. ✅ 测试AI功能集成
3. ✅ 配置X平台连接
4. ✅ 测试社交媒体发布功能

完成这些步骤后，你的Postiz开发环境就完全配置好了！
